<?php
/**
 * 渗透测试靶场 - 自动化部署脚本
 * 用于快速设置数据库和初始化系统
 */

// 检查是否通过命令行运行
if (php_sapi_name() !== 'cli') {
    die("此脚本只能通过命令行运行！\n");
}

echo "=================================\n";
echo "渗透测试靶场 - 自动化部署脚本\n";
echo "=================================\n\n";

// 检查PHP版本
if (version_compare(PHP_VERSION, '7.4.0') < 0) {
    die("错误: 需要PHP 7.4或更高版本，当前版本: " . PHP_VERSION . "\n");
}

// 检查必需的PHP扩展
$required_extensions = ['pdo', 'pdo_mysql', 'mysqli'];
foreach ($required_extensions as $ext) {
    if (!extension_loaded($ext)) {
        die("错误: 缺少PHP扩展: $ext\n");
    }
}

echo "✓ PHP版本检查通过: " . PHP_VERSION . "\n";
echo "✓ PHP扩展检查通过\n\n";

// 获取数据库配置
echo "请输入数据库配置信息:\n";
echo "数据库主机 [localhost]: ";
$db_host = trim(fgets(STDIN));
if (empty($db_host)) $db_host = 'localhost';

echo "数据库用户名 [root]: ";
$db_user = trim(fgets(STDIN));
if (empty($db_user)) $db_user = 'root';

echo "数据库密码: ";
$db_pass = trim(fgets(STDIN));

echo "数据库名称 [pentest_lab]: ";
$db_name = trim(fgets(STDIN));
if (empty($db_name)) $db_name = 'pentest_lab';

echo "\n正在测试数据库连接...\n";

// 测试数据库连接
try {
    $pdo = new PDO("mysql:host=$db_host", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✓ 数据库连接成功\n";
} catch (PDOException $e) {
    die("错误: 数据库连接失败: " . $e->getMessage() . "\n");
}

// 创建数据库
echo "正在创建数据库...\n";
try {
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$db_name` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    $pdo->exec("USE `$db_name`");
    echo "✓ 数据库创建成功\n";
} catch (PDOException $e) {
    die("错误: 创建数据库失败: " . $e->getMessage() . "\n");
}

// 导入数据库结构
echo "正在导入数据库结构...\n";
$sql_file = __DIR__ . '/config/database.sql';
if (!file_exists($sql_file)) {
    die("错误: 找不到数据库结构文件: $sql_file\n");
}

$sql = file_get_contents($sql_file);
$statements = explode(';', $sql);

foreach ($statements as $statement) {
    $statement = trim($statement);
    if (!empty($statement)) {
        try {
            $pdo->exec($statement);
        } catch (PDOException $e) {
            echo "警告: SQL执行失败: " . $e->getMessage() . "\n";
        }
    }
}

echo "✓ 数据库结构导入完成\n";

// 更新配置文件
echo "正在更新配置文件...\n";
$config_file = __DIR__ . '/config/database.php';
$config_content = file_get_contents($config_file);

$config_content = str_replace("define('DB_HOST', 'localhost');", "define('DB_HOST', '$db_host');", $config_content);
$config_content = str_replace("define('DB_NAME', 'pentest_lab');", "define('DB_NAME', '$db_name');", $config_content);
$config_content = str_replace("define('DB_USER', 'root');", "define('DB_USER', '$db_user');", $config_content);
$config_content = str_replace("define('DB_PASS', '');", "define('DB_PASS', '$db_pass');", $config_content);

file_put_contents($config_file, $config_content);
echo "✓ 配置文件更新完成\n";

// 设置文件权限
echo "正在设置文件权限...\n";
$upload_dir = __DIR__ . '/upload';
if (!is_dir($upload_dir)) {
    mkdir($upload_dir, 0755, true);
}
chmod($upload_dir, 0755);
echo "✓ 文件权限设置完成\n";

// 创建默认头像
echo "正在创建默认资源...\n";
$default_avatar = __DIR__ . '/upload/default.jpg';
if (!file_exists($default_avatar)) {
    // 创建一个简单的默认头像占位符
    copy(__DIR__ . '/assets/images/default-avatar.png', $default_avatar);
}
echo "✓ 默认资源创建完成\n";

echo "\n=================================\n";
echo "部署完成！\n";
echo "=================================\n\n";

echo "访问信息:\n";
echo "首页: http://localhost/\n";
echo "登录页面: http://localhost/login.php\n\n";

echo "默认账户:\n";
echo "管理员: admin / admin123\n";
echo "用户: test / test123\n";
echo "演示: demo / demo123\n\n";

echo "漏洞信息:\n";
echo "1. SQL注入: 登录页面用户名参数\n";
echo "2. 文件上传: 头像上传功能\n\n";

echo "安全警告:\n";
echo "本系统包含故意设置的安全漏洞，仅用于教育目的！\n";
echo "请勿在生产环境中部署！\n\n";

echo "启动开发服务器:\n";
echo "php -S localhost:8080\n\n";

echo "查看详细文档:\n";
echo "cat README.md\n\n";
?>
