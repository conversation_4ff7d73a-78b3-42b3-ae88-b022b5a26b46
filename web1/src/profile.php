<?php
session_start();
require_once 'config/database.php';
require_once 'includes/session_check.php';

requireLogin();

// 获取用户信息
$db = getDatabase();
$conn = $db->getConnection();

$stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
$stmt->execute([$_SESSION['user_id']]);
$user = $stmt->fetch();

$message = '';
$message_type = '';

// 处理个人资料更新
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['update_profile'])) {
    $email = $_POST['email'] ?? '';
    
    if (!empty($email) && filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $stmt = $conn->prepare("UPDATE users SET email = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
        if ($stmt->execute([$email, $_SESSION['user_id']])) {
            $message = '个人资料更新成功！';
            $message_type = 'success';
            $user['email'] = $email;
            
            logSystemAction('profile_update', $_SESSION['user_id'], 'Profile updated');
        } else {
            $message = '更新失败，请重试！';
            $message_type = 'danger';
        }
    } else {
        $message = '请输入有效的邮箱地址！';
        $message_type = 'danger';
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人资料 - 渗透测试靶场</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/dashboard.css" rel="stylesheet">
</head>
<body>
    <!-- 侧边栏 -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="sidebar-brand">
                <i class="fas fa-shield-alt me-2"></i>
                <span class="brand-text">渗透测试靶场</span>
            </div>
            <button class="sidebar-toggle d-lg-none" onclick="toggleSidebar()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <div class="sidebar-menu">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link" href="dashboard.php">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>仪表板</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" href="profile.php">
                        <i class="fas fa-user"></i>
                        <span>个人资料</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="upload.php">
                        <i class="fas fa-upload"></i>
                        <span>文件上传</span>
                    </a>
                </li>
                <?php if ($user['role'] == 'admin'): ?>
                <li class="nav-item">
                    <a class="nav-link" href="users.php">
                        <i class="fas fa-users"></i>
                        <span>用户管理</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="logs.php">
                        <i class="fas fa-list-alt"></i>
                        <span>系统日志</span>
                    </a>
                </li>
                <?php endif; ?>
                <li class="nav-item">
                    <a class="nav-link" href="settings.php">
                        <i class="fas fa-cog"></i>
                        <span>设置</span>
                    </a>
                </li>
            </ul>
        </div>
        
        <div class="sidebar-footer">
            <div class="user-info">
                <div class="user-avatar">
                    <img src="upload/<?php echo htmlspecialchars($user['avatar']); ?>" alt="头像" onerror="this.src='assets/images/default-avatar.png'">
                </div>
                <div class="user-details">
                    <div class="user-name"><?php echo htmlspecialchars($user['username']); ?></div>
                    <div class="user-role"><?php echo $user['role'] == 'admin' ? '管理员' : '用户'; ?></div>
                </div>
            </div>
        </div>
    </nav>
    
    <!-- 主内容区域 -->
    <div class="main-content">
        <!-- 顶部导航栏 -->
        <header class="topbar">
            <div class="topbar-left">
                <button class="sidebar-toggle d-lg-none" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                <h1 class="page-title">个人资料</h1>
            </div>
            
            <div class="topbar-right">
                <div class="topbar-item">
                    <a href="dashboard.php" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-arrow-left me-2"></i>返回仪表板
                    </a>
                </div>
                
                <div class="topbar-item dropdown">
                    <button class="btn btn-link dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <img src="upload/<?php echo htmlspecialchars($user['avatar']); ?>" alt="头像" class="topbar-avatar" onerror="this.src='assets/images/default-avatar.png'">
                        <span class="ms-2"><?php echo htmlspecialchars($user['username']); ?></span>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i>个人资料</a></li>
                        <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog me-2"></i>设置</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="dashboard.php?logout=1"><i class="fas fa-sign-out-alt me-2"></i>退出登录</a></li>
                    </ul>
                </div>
            </div>
        </header>
        
        <!-- 页面内容 -->
        <main class="content">
            <div class="container-fluid">
                <?php if ($message): ?>
                    <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                        <i class="fas fa-<?php echo $message_type == 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                        <?php echo htmlspecialchars($message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <div class="row">
                    <!-- 个人信息卡片 -->
                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-body text-center">
                                <div class="profile-avatar mb-3">
                                    <img src="upload/<?php echo htmlspecialchars($user['avatar']); ?>" alt="头像" class="rounded-circle" style="width: 120px; height: 120px; object-fit: cover;" onerror="this.src='assets/images/default-avatar.png'">
                                </div>
                                <h4><?php echo htmlspecialchars($user['username']); ?></h4>
                                <p class="text-muted"><?php echo $user['role'] == 'admin' ? '系统管理员' : '普通用户'; ?></p>
                                <a href="upload.php" class="btn btn-primary">
                                    <i class="fas fa-camera me-2"></i>更换头像
                                </a>
                            </div>
                        </div>
                        
                        <div class="card mt-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-info-circle me-2"></i>账户信息
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="info-row">
                                    <span class="info-label">用户ID:</span>
                                    <span class="info-value"><?php echo $user['id']; ?></span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">注册时间:</span>
                                    <span class="info-value"><?php echo date('Y-m-d', strtotime($user['created_at'])); ?></span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">最后更新:</span>
                                    <span class="info-value"><?php echo date('Y-m-d H:i', strtotime($user['updated_at'])); ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 编辑表单 -->
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-edit me-2"></i>编辑个人资料
                                </h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" action="">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="username" class="form-label">用户名</label>
                                                <input type="text" class="form-control" id="username" value="<?php echo htmlspecialchars($user['username']); ?>" readonly>
                                                <div class="form-text">用户名不可修改</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="role" class="form-label">用户角色</label>
                                                <input type="text" class="form-control" id="role" value="<?php echo $user['role'] == 'admin' ? '管理员' : '普通用户'; ?>" readonly>
                                                <div class="form-text">角色由管理员分配</div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="email" class="form-label">邮箱地址</label>
                                        <input type="email" class="form-control" id="email" name="email" value="<?php echo htmlspecialchars($user['email'] ?? ''); ?>" placeholder="请输入邮箱地址">
                                        <div class="form-text">用于接收系统通知和密码重置</div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="password" class="form-label">密码</label>
                                        <input type="password" class="form-control" id="password" value="********" readonly>
                                        <div class="form-text">
                                            <a href="change_password.php">点击这里修改密码</a>
                                        </div>
                                    </div>
                                    
                                    <div class="d-flex justify-content-between">
                                        <button type="submit" name="update_profile" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>保存更改
                                        </button>
                                        <a href="dashboard.php" class="btn btn-secondary">
                                            <i class="fas fa-times me-2"></i>取消
                                        </a>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/dashboard.js"></script>
    
    <style>
        .info-row {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .info-row:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-weight: 600;
            color: #6c757d;
        }
        
        .info-value {
            color: #495057;
        }
    </style>
</body>
</html>
