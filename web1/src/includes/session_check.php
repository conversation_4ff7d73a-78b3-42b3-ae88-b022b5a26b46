<?php
/**
 * 会话检查文件
 * 用于验证用户登录状态
 */

// 开始会话
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

/**
 * 检查用户是否已登录
 * @return bool
 */
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

/**
 * 要求用户登录，如果未登录则重定向到登录页面
 */
function requireLogin() {
    if (!isLoggedIn()) {
        header('Location: login.php');
        exit();
    }
}

/**
 * 检查用户是否为管理员
 * @return bool
 */
function isAdmin() {
    return isset($_SESSION['role']) && $_SESSION['role'] === 'admin';
}

/**
 * 要求管理员权限
 */
function requireAdmin() {
    requireLogin();
    if (!isAdmin()) {
        header('Location: dashboard.php');
        exit();
    }
}

/**
 * 获取当前用户ID
 * @return int|null
 */
function getCurrentUserId() {
    return $_SESSION['user_id'] ?? null;
}

/**
 * 获取当前用户名
 * @return string|null
 */
function getCurrentUsername() {
    return $_SESSION['username'] ?? null;
}

/**
 * 获取当前用户角色
 * @return string|null
 */
function getCurrentUserRole() {
    return $_SESSION['role'] ?? null;
}

/**
 * 安全退出登录
 */
function logout() {
    // 记录退出日志
    if (isLoggedIn()) {
        require_once 'config/database.php';
        logSystemAction('logout', getCurrentUserId(), 'User logged out');
    }
    
    // 清除会话
    session_unset();
    session_destroy();
    
    // 重定向到登录页面
    header('Location: login.php');
    exit();
}

/**
 * 生成CSRF令牌
 * @return string
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * 验证CSRF令牌
 * @param string $token
 * @return bool
 */
function validateCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * 输出CSRF令牌的隐藏字段
 */
function csrfField() {
    echo '<input type="hidden" name="csrf_token" value="' . htmlspecialchars(generateCSRFToken()) . '">';
}

/**
 * 检查会话是否过期
 * @param int $timeout 超时时间（秒）
 * @return bool
 */
function isSessionExpired($timeout = 3600) {
    if (isset($_SESSION['last_activity'])) {
        return (time() - $_SESSION['last_activity']) > $timeout;
    }
    return false;
}

/**
 * 更新会话活动时间
 */
function updateSessionActivity() {
    $_SESSION['last_activity'] = time();
}

/**
 * 安全的会话重新生成
 */
function regenerateSession() {
    session_regenerate_id(true);
}

// 自动更新会话活动时间
if (isLoggedIn()) {
    // 检查会话是否过期
    if (isSessionExpired()) {
        logout();
    }
    
    // 更新活动时间
    updateSessionActivity();
    
    // 定期重新生成会话ID（每30分钟）
    if (!isset($_SESSION['last_regeneration']) || (time() - $_SESSION['last_regeneration']) > 1800) {
        regenerateSession();
        $_SESSION['last_regeneration'] = time();
    }
}
?>
