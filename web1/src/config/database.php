<?php
/**
 * 数据库配置文件
 * 渗透测试靶场 - 数据库连接配置
 */

// 数据库配置
define('DB_HOST', 'localhost');
define('DB_NAME', 'pentest_lab');
define('DB_USER', 'root');
define('DB_PASS', 'root');
define('DB_CHARSET', 'utf8mb4');

class Database {
    private $host = DB_HOST;
    private $db_name = DB_NAME;
    private $username = DB_USER;
    private $password = DB_PASS;
    private $charset = DB_CHARSET;
    public $conn;

    /**
     * 获取数据库连接
     * @return PDO|null
     */
    public function getConnection() {
        $this->conn = null;
        
        try {
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
            $this->conn = new PDO($dsn, $this->username, $this->password);
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        } catch(PDOException $exception) {
            echo "数据库连接失败: " . $exception->getMessage();
        }
        
        return $this->conn;
    }

    /**
     * 不安全的数据库连接（用于SQL注入演示）
     * @return mysqli|false
     */
    public function getUnsafeConnection() {
        $connection = mysqli_connect($this->host, $this->username, $this->password, $this->db_name);
        
        if (!$connection) {
            die("数据库连接失败: " . mysqli_connect_error());
        }
        
        mysqli_set_charset($connection, $this->charset);
        return $connection;
    }

    /**
     * 关闭数据库连接
     */
    public function closeConnection() {
        $this->conn = null;
    }
}

/**
 * 获取数据库实例
 * @return Database
 */
function getDatabase() {
    return new Database();
}

/**
 * 记录系统日志
 * @param string $action 操作类型
 * @param int|null $user_id 用户ID
 * @param string $details 详细信息
 */
function logSystemAction($action, $user_id = null, $details = '') {
    $db = getDatabase();
    $conn = $db->getConnection();
    
    if ($conn) {
        try {
            $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
            $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
            
            $stmt = $conn->prepare("INSERT INTO system_logs (user_id, action, ip_address, user_agent, details) VALUES (?, ?, ?, ?, ?)");
            $stmt->execute([$user_id, $action, $ip, $user_agent, $details]);
        } catch (Exception $e) {
            // 静默处理日志错误
        }
    }
}
?>
