/* 渗透测试靶场 - 后台管理界面样式 */

:root {
    --sidebar-width: 280px;
    --topbar-height: 70px;
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --info-color: #17a2b8;
    --dark-color: #34495e;
    --light-color: #ecf0f1;
    --sidebar-bg: #2c3e50;
    --sidebar-hover: #34495e;
    --content-bg: #f8f9fa;
    --card-shadow: 0 2px 10px rgba(0,0,0,0.1);
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: var(--content-bg);
    overflow-x: hidden;
}

/* 侧边栏样式 */
.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: var(--sidebar-width);
    height: 100vh;
    background: var(--sidebar-bg);
    color: white;
    z-index: 1000;
    transition: var(--transition);
    display: flex;
    flex-direction: column;
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.sidebar-brand {
    display: flex;
    align-items: center;
    font-size: 1.2rem;
    font-weight: 600;
}

.brand-text {
    margin-left: 8px;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: var(--transition);
}

.sidebar-toggle:hover {
    background: rgba(255,255,255,0.1);
}

.sidebar-menu {
    flex: 1;
    padding: 20px 0;
    overflow-y: auto;
}

.sidebar-menu .nav-link {
    color: rgba(255,255,255,0.8);
    padding: 12px 20px;
    display: flex;
    align-items: center;
    text-decoration: none;
    transition: var(--transition);
    border-left: 3px solid transparent;
}

.sidebar-menu .nav-link:hover {
    color: white;
    background: var(--sidebar-hover);
    border-left-color: var(--secondary-color);
}

.sidebar-menu .nav-link.active {
    color: white;
    background: var(--sidebar-hover);
    border-left-color: var(--secondary-color);
}

.sidebar-menu .nav-link i {
    width: 20px;
    margin-right: 12px;
    text-align: center;
}

.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255,255,255,0.1);
}

.user-info {
    display: flex;
    align-items: center;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 12px;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-details {
    flex: 1;
}

.user-name {
    font-weight: 600;
    font-size: 0.9rem;
}

.user-role {
    font-size: 0.8rem;
    color: rgba(255,255,255,0.7);
}

/* 主内容区域 */
.main-content {
    margin-left: var(--sidebar-width);
    min-height: 100vh;
    transition: var(--transition);
}

/* 顶部导航栏 */
.topbar {
    height: var(--topbar-height);
    background: white;
    border-bottom: 1px solid #e9ecef;
    padding: 0 30px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: var(--card-shadow);
    position: sticky;
    top: 0;
    z-index: 999;
}

.topbar-left {
    display: flex;
    align-items: center;
}

.topbar-left .sidebar-toggle {
    color: var(--dark-color);
    margin-right: 15px;
}

.page-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--dark-color);
    margin: 0;
}

.topbar-right {
    display: flex;
    align-items: center;
    gap: 15px;
}

.topbar-item {
    display: flex;
    align-items: center;
}

.topbar-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
}

/* 内容区域 */
.content {
    padding: 30px;
}

/* 欢迎区域 */
.welcome-section {
    margin-bottom: 30px;
}

.welcome-card {
    background: linear-gradient(135deg, var(--secondary-color), #5dade2);
    color: white;
    padding: 30px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: var(--card-shadow);
}

.welcome-content h2 {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 8px;
}

.welcome-content p {
    font-size: 1rem;
    opacity: 0.9;
    margin: 0;
}

.welcome-actions {
    display: flex;
    gap: 10px;
}

.welcome-actions .btn {
    border-color: rgba(255,255,255,0.3);
}

.welcome-actions .btn-outline-primary {
    color: white;
    border-color: white;
}

.welcome-actions .btn-outline-primary:hover {
    background: white;
    color: var(--secondary-color);
}

/* 统计卡片 */
.stats-section {
    margin-bottom: 30px;
}

.stats-card {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: var(--card-shadow);
    display: flex;
    align-items: center;
    transition: var(--transition);
    border-left: 4px solid;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 25px rgba(0,0,0,0.15);
}

.stats-primary {
    border-left-color: var(--secondary-color);
}

.stats-success {
    border-left-color: var(--success-color);
}

.stats-warning {
    border-left-color: var(--warning-color);
}

.stats-info {
    border-left-color: var(--info-color);
}

.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-right: 20px;
}

.stats-primary .stats-icon {
    background: rgba(52, 152, 219, 0.1);
    color: var(--secondary-color);
}

.stats-success .stats-icon {
    background: rgba(39, 174, 96, 0.1);
    color: var(--success-color);
}

.stats-warning .stats-icon {
    background: rgba(243, 156, 18, 0.1);
    color: var(--warning-color);
}

.stats-info .stats-icon {
    background: rgba(23, 162, 184, 0.1);
    color: var(--info-color);
}

.stats-content {
    flex: 1;
}

.stats-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--dark-color);
    line-height: 1;
}

.stats-label {
    font-size: 0.9rem;
    color: #6c757d;
    margin-top: 5px;
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: 12px;
    box-shadow: var(--card-shadow);
    transition: var(--transition);
}

.card:hover {
    box-shadow: 0 5px 25px rgba(0,0,0,0.15);
}

.card-header {
    background: white;
    border-bottom: 1px solid #e9ecef;
    padding: 20px 25px;
    border-radius: 12px 12px 0 0 !important;
}

.card-title {
    color: var(--dark-color);
    font-weight: 600;
}

.card-body {
    padding: 25px;
}

/* 表格样式 */
.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    border-bottom: 2px solid #e9ecef;
    font-weight: 600;
    color: var(--dark-color);
    padding: 15px;
}

.table td {
    padding: 15px;
    vertical-align: middle;
    border-top: 1px solid #e9ecef;
}

.table-hover tbody tr:hover {
    background: rgba(52, 152, 219, 0.05);
}

/* 响应式设计 */
@media (max-width: 991.98px) {
    .sidebar {
        transform: translateX(-100%);
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .topbar {
        padding: 0 15px;
    }
    
    .content {
        padding: 20px 15px;
    }
    
    .welcome-card {
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }
    
    .welcome-actions {
        justify-content: center;
    }
}

@media (max-width: 767.98px) {
    .stats-card {
        margin-bottom: 15px;
    }
    
    .welcome-card {
        padding: 20px;
    }
    
    .welcome-content h2 {
        font-size: 1.5rem;
    }
    
    .stats-number {
        font-size: 1.5rem;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.stats-card,
.card,
.welcome-card {
    animation: fadeInUp 0.6s ease-out;
}

/* 滚动条样式 */
.sidebar-menu::-webkit-scrollbar {
    width: 4px;
}

.sidebar-menu::-webkit-scrollbar-track {
    background: rgba(255,255,255,0.1);
}

.sidebar-menu::-webkit-scrollbar-thumb {
    background: rgba(255,255,255,0.3);
    border-radius: 2px;
}

.sidebar-menu::-webkit-scrollbar-thumb:hover {
    background: rgba(255,255,255,0.5);
}
