/**
 * 渗透测试靶场 - 后台管理界面JavaScript
 */

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    initializeDashboard();
});

/**
 * 初始化后台管理界面
 */
function initializeDashboard() {
    // 初始化侧边栏
    initializeSidebar();
    
    // 初始化统计卡片动画
    initializeStatsCards();
    
    // 初始化表格功能
    initializeTables();
    
    // 初始化工具提示
    initializeTooltips();
    
    // 初始化实时时间
    initializeRealTime();
    
    // 添加键盘快捷键
    addKeyboardShortcuts();
}

/**
 * 初始化侧边栏
 */
function initializeSidebar() {
    const sidebar = document.getElementById('sidebar');
    const toggleButtons = document.querySelectorAll('.sidebar-toggle');
    
    // 侧边栏切换功能
    toggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            toggleSidebar();
        });
    });
    
    // 点击外部区域关闭侧边栏（移动端）
    document.addEventListener('click', function(e) {
        if (window.innerWidth <= 991.98) {
            if (!sidebar.contains(e.target) && !e.target.closest('.sidebar-toggle')) {
                sidebar.classList.remove('show');
            }
        }
    });
    
    // 窗口大小改变时的处理
    window.addEventListener('resize', function() {
        if (window.innerWidth > 991.98) {
            sidebar.classList.remove('show');
        }
    });
}

/**
 * 切换侧边栏显示/隐藏
 */
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    sidebar.classList.toggle('show');
}

/**
 * 初始化统计卡片动画
 */
function initializeStatsCards() {
    const statsCards = document.querySelectorAll('.stats-card');
    
    // 添加悬停效果
    statsCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
    
    // 数字动画效果
    animateNumbers();
}

/**
 * 数字动画效果
 */
function animateNumbers() {
    const numberElements = document.querySelectorAll('.stats-number');
    
    numberElements.forEach(element => {
        const finalNumber = parseInt(element.textContent);
        if (isNaN(finalNumber)) return;
        
        let currentNumber = 0;
        const increment = Math.ceil(finalNumber / 50);
        const timer = setInterval(() => {
            currentNumber += increment;
            if (currentNumber >= finalNumber) {
                currentNumber = finalNumber;
                clearInterval(timer);
            }
            element.textContent = currentNumber;
        }, 30);
    });
}

/**
 * 初始化表格功能
 */
function initializeTables() {
    const tables = document.querySelectorAll('.table');
    
    tables.forEach(table => {
        // 添加表格行点击效果
        const rows = table.querySelectorAll('tbody tr');
        rows.forEach(row => {
            row.addEventListener('click', function() {
                // 移除其他行的选中状态
                rows.forEach(r => r.classList.remove('table-active'));
                // 添加当前行的选中状态
                this.classList.add('table-active');
            });
        });
    });
}

/**
 * 初始化工具提示
 */
function initializeTooltips() {
    // 初始化Bootstrap工具提示
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

/**
 * 初始化实时时间
 */
function initializeRealTime() {
    const timeElement = document.querySelector('.stats-info .stats-number');
    if (timeElement && timeElement.textContent.includes(':')) {
        setInterval(() => {
            const now = new Date();
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            timeElement.textContent = `${hours}:${minutes}`;
        }, 1000);
    }
}

/**
 * 刷新数据
 */
function refreshData() {
    const refreshBtn = document.querySelector('[onclick="refreshData()"]');
    const originalText = refreshBtn.innerHTML;
    
    // 显示加载状态
    refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 刷新中...';
    refreshBtn.disabled = true;
    
    // 模拟数据刷新
    setTimeout(() => {
        // 重新加载页面
        window.location.reload();
    }, 1000);
}

/**
 * 添加键盘快捷键
 */
function addKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + B: 切换侧边栏
        if ((e.ctrlKey || e.metaKey) && e.key === 'b') {
            e.preventDefault();
            toggleSidebar();
        }
        
        // Ctrl/Cmd + R: 刷新数据
        if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
            e.preventDefault();
            refreshData();
        }
        
        // Escape: 关闭侧边栏（移动端）
        if (e.key === 'Escape' && window.innerWidth <= 991.98) {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.remove('show');
        }
    });
}

/**
 * 显示通知消息
 */
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    // 自动关闭通知
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

/**
 * 格式化文件大小
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 格式化时间
 */
function formatTime(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;
    
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);
    
    if (minutes < 1) return '刚刚';
    if (minutes < 60) return `${minutes}分钟前`;
    if (hours < 24) return `${hours}小时前`;
    if (days < 7) return `${days}天前`;
    
    return date.toLocaleDateString('zh-CN');
}

/**
 * 复制到剪贴板
 */
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showNotification('已复制到剪贴板', 'success');
    }).catch(() => {
        showNotification('复制失败', 'danger');
    });
}

/**
 * 确认对话框
 */
function confirmAction(message, callback) {
    if (confirm(message)) {
        callback();
    }
}

/**
 * 平滑滚动到顶部
 */
function scrollToTop() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
}

// 添加返回顶部按钮
window.addEventListener('scroll', function() {
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    let backToTopBtn = document.getElementById('backToTop');
    
    if (scrollTop > 300) {
        if (!backToTopBtn) {
            backToTopBtn = document.createElement('button');
            backToTopBtn.id = 'backToTop';
            backToTopBtn.className = 'btn btn-primary position-fixed';
            backToTopBtn.style.cssText = 'bottom: 20px; right: 20px; z-index: 999; border-radius: 50%; width: 50px; height: 50px;';
            backToTopBtn.innerHTML = '<i class="fas fa-arrow-up"></i>';
            backToTopBtn.onclick = scrollToTop;
            document.body.appendChild(backToTopBtn);
        }
        backToTopBtn.style.display = 'block';
    } else if (backToTopBtn) {
        backToTopBtn.style.display = 'none';
    }
});

// 添加CSS样式
const style = document.createElement('style');
style.textContent = `
    .table-active {
        background-color: rgba(52, 152, 219, 0.1) !important;
    }
    
    #backToTop {
        transition: all 0.3s ease;
        box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    }
    
    #backToTop:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.3);
    }
    
    .sidebar.show {
        transform: translateX(0) !important;
    }
    
    @media (max-width: 991.98px) {
        .sidebar {
            transform: translateX(-100%);
        }
    }
`;
document.head.appendChild(style);
