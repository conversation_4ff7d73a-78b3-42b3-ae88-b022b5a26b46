/**
 * 渗透测试靶场 - 文件上传JavaScript
 * 包含可绕过的前端黑名单验证（故意设置的安全漏洞）
 */

// 黑名单文件扩展名（故意设置得不完整，容易绕过）
const BLACKLISTED_EXTENSIONS = [
    'php', 'exe', 'bat', 'cmd', 'com', 'scr', 'vbs', 'js'
];

// 最大文件大小 (10MB)
const MAX_FILE_SIZE = 10 * 1024 * 1024;

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    initializeUpload();
});

/**
 * 初始化文件上传功能
 */
function initializeUpload() {
    const uploadArea = document.getElementById('uploadArea');
    const fileInput = document.getElementById('fileInput');
    const uploadForm = document.getElementById('uploadForm');
    
    // 拖拽上传功能
    setupDragAndDrop(uploadArea, fileInput);
    
    // 文件选择事件
    fileInput.addEventListener('change', handleFileSelect);
    
    // 表单提交事件
    uploadForm.addEventListener('submit', handleFormSubmit);
    
    // 点击上传区域选择文件
    uploadArea.addEventListener('click', function() {
        fileInput.click();
    });
}

/**
 * 设置拖拽上传功能
 */
function setupDragAndDrop(uploadArea, fileInput) {
    // 阻止默认拖拽行为
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        uploadArea.addEventListener(eventName, preventDefaults, false);
        document.body.addEventListener(eventName, preventDefaults, false);
    });
    
    // 拖拽进入和悬停效果
    ['dragenter', 'dragover'].forEach(eventName => {
        uploadArea.addEventListener(eventName, highlight, false);
    });
    
    // 拖拽离开效果
    ['dragleave', 'drop'].forEach(eventName => {
        uploadArea.addEventListener(eventName, unhighlight, false);
    });
    
    // 文件放置事件
    uploadArea.addEventListener('drop', handleDrop, false);
    
    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }
    
    function highlight() {
        uploadArea.classList.add('dragover');
    }
    
    function unhighlight() {
        uploadArea.classList.remove('dragover');
    }
    
    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;
        
        if (files.length > 0) {
            fileInput.files = files;
            handleFileSelect({ target: { files: files } });
        }
    }
}

/**
 * 处理文件选择
 */
function handleFileSelect(event) {
    const files = event.target.files;
    if (files.length === 0) return;
    
    const file = files[0];
    
    // 显示文件预览
    showFilePreview(file);
    
    // 客户端验证（故意设置得容易绕过）
    if (!validateFile(file)) {
        return;
    }
    
    console.log('文件验证通过:', file.name);
}

/**
 * 文件验证（故意设置的弱验证，容易绕过）
 */
function validateFile(file) {
    // 文件大小验证
    if (file.size > MAX_FILE_SIZE) {
        showAlert('文件大小不能超过10MB！', 'danger');
        clearFileInput();
        return false;
    }
    
    // 文件扩展名验证（故意设置得不严格）
    const fileName = file.name.toLowerCase();
    const fileExtension = fileName.split('.').pop();
    
    // 简单的黑名单检查（容易绕过）
    if (BLACKLISTED_EXTENSIONS.includes(fileExtension)) {
        showAlert(`不允许上传 .${fileExtension} 文件！`, 'danger');
        clearFileInput();
        return false;
    }
    
    // 故意遗漏的检查：
    // 1. 没有检查文件的真实MIME类型
    // 2. 没有检查双扩展名（如 file.php.jpg）
    // 3. 没有检查大小写变体（如 .PHP）
    // 4. 没有检查空字节注入
    // 5. 黑名单不完整，遗漏了很多危险扩展名
    
    return true;
}

/**
 * 显示文件预览
 */
function showFilePreview(file) {
    let preview = document.getElementById('filePreview');
    
    if (!preview) {
        preview = document.createElement('div');
        preview.id = 'filePreview';
        preview.className = 'file-preview';
        document.querySelector('.upload-form').appendChild(preview);
    }
    
    const fileType = getFileType(file.name);
    const fileIcon = getFileIcon(fileType);
    
    preview.innerHTML = `
        <div class="preview-item file-type-${fileType}">
            <div class="preview-icon">
                <i class="fas ${fileIcon}"></i>
            </div>
            <div class="preview-info">
                <div class="preview-name">${file.name}</div>
                <div class="preview-size">${formatFileSize(file.size)}</div>
            </div>
            <button type="button" class="preview-remove" onclick="clearFileInput()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    
    preview.classList.add('show');
}

/**
 * 获取文件类型
 */
function getFileType(fileName) {
    const extension = fileName.toLowerCase().split('.').pop();
    
    const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'];
    const documentTypes = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'];
    const archiveTypes = ['zip', 'rar', '7z', 'tar', 'gz'];
    const videoTypes = ['mp4', 'avi', 'mkv', 'mov', 'wmv', 'flv'];
    const audioTypes = ['mp3', 'wav', 'flac', 'aac', 'ogg'];
    const codeTypes = ['html', 'css', 'js', 'php', 'py', 'java', 'cpp', 'c'];
    const executableTypes = ['exe', 'msi', 'deb', 'rpm', 'dmg'];
    
    if (imageTypes.includes(extension)) return 'image';
    if (documentTypes.includes(extension)) return 'document';
    if (archiveTypes.includes(extension)) return 'archive';
    if (videoTypes.includes(extension)) return 'video';
    if (audioTypes.includes(extension)) return 'audio';
    if (codeTypes.includes(extension)) return 'code';
    if (executableTypes.includes(extension)) return 'executable';
    
    return 'document';
}

/**
 * 获取文件图标
 */
function getFileIcon(fileType) {
    const icons = {
        'image': 'fa-image',
        'document': 'fa-file-alt',
        'archive': 'fa-file-archive',
        'video': 'fa-video',
        'audio': 'fa-music',
        'code': 'fa-code',
        'executable': 'fa-cog'
    };
    
    return icons[fileType] || 'fa-file';
}

/**
 * 格式化文件大小
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 处理表单提交
 */
function handleFormSubmit(event) {
    const fileInput = document.getElementById('fileInput');
    
    if (!fileInput.files || fileInput.files.length === 0) {
        event.preventDefault();
        showAlert('请选择要上传的文件！', 'warning');
        return false;
    }
    
    const file = fileInput.files[0];
    
    // 再次验证文件（前端验证，容易绕过）
    if (!validateFile(file)) {
        event.preventDefault();
        return false;
    }
    
    // 显示上传进度
    showUploadProgress();
    
    // 禁用上传按钮
    const uploadBtn = document.getElementById('uploadBtn');
    uploadBtn.disabled = true;
    uploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>上传中...';
    
    return true;
}

/**
 * 显示上传进度
 */
function showUploadProgress() {
    const progressContainer = document.getElementById('uploadProgress');
    const progressBar = progressContainer.querySelector('.progress-bar');
    const progressText = progressContainer.querySelector('.progress-text');
    
    progressContainer.style.display = 'block';
    
    // 模拟上传进度
    let progress = 0;
    const interval = setInterval(() => {
        progress += Math.random() * 15;
        if (progress > 90) progress = 90;
        
        progressBar.style.width = progress + '%';
        progressText.textContent = `上传中... ${Math.round(progress)}%`;
        
        if (progress >= 90) {
            clearInterval(interval);
            progressText.textContent = '处理中...';
        }
    }, 200);
}

/**
 * 清除文件输入
 */
function clearFileInput() {
    const fileInput = document.getElementById('fileInput');
    const preview = document.getElementById('filePreview');
    
    fileInput.value = '';
    
    if (preview) {
        preview.classList.remove('show');
        setTimeout(() => {
            preview.remove();
        }, 300);
    }
}

/**
 * 清除上传
 */
function clearUpload() {
    clearFileInput();
    
    const progressContainer = document.getElementById('uploadProgress');
    const uploadBtn = document.getElementById('uploadBtn');
    
    progressContainer.style.display = 'none';
    uploadBtn.disabled = false;
    uploadBtn.innerHTML = '<i class="fas fa-upload me-2"></i>上传文件';
}

/**
 * 显示警告消息
 */
function showAlert(message, type = 'info') {
    const alertContainer = document.querySelector('.container-fluid');
    const alert = document.createElement('div');
    alert.className = `alert alert-${type} alert-dismissible fade show`;
    alert.innerHTML = `
        <i class="fas fa-${type === 'danger' ? 'exclamation-triangle' : type === 'success' ? 'check-circle' : 'info-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    alertContainer.insertBefore(alert, alertContainer.firstChild);
    
    // 自动关闭警告
    setTimeout(() => {
        if (alert.parentElement) {
            alert.remove();
        }
    }, 5000);
}

/**
 * 删除文件
 */
function deleteFile(fileId) {
    if (confirm('确定要删除这个文件吗？')) {
        // 这里应该发送AJAX请求到服务器删除文件
        // 为了演示，我们只是显示一个消息
        showAlert('文件删除功能需要在服务端实现', 'info');
    }
}

// 绕过前端验证的方法（供渗透测试学习）：
// 1. 修改文件扩展名：将 shell.php 重命名为 shell.jpg
// 2. 双扩展名：使用 shell.php.jpg
// 3. 大小写绕过：使用 shell.PHP 或 shell.Php
// 4. 空字节注入：shell.php%00.jpg（在某些环境下有效）
// 5. 使用开发者工具修改JavaScript代码
// 6. 禁用JavaScript后上传
// 7. 使用Burp Suite等工具拦截并修改请求
// 8. 上传.htaccess文件来执行其他文件类型
