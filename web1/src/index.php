<?php
session_start();

// 如果已经登录，重定向到后台
if (isset($_SESSION['user_id'])) {
    header('Location: dashboard.php');
    exit();
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络安全管理系统 - 首页</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--gradient-primary);
            min-height: 100vh;
        }
        
        .hero-section {
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
        }
        
        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            pointer-events: none;
        }
        
        .hero-content {
            position: relative;
            z-index: 1;
        }
        
        .hero-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.2);
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 60px 40px;
            text-align: center;
            animation: slideInUp 0.8s ease-out;
        }
        
        .hero-icon {
            font-size: 4rem;
            color: var(--secondary-color);
            margin-bottom: 30px;
            animation: pulse 2s infinite;
        }
        
        .hero-title {
            font-size: 3rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 20px;
        }
        
        .hero-subtitle {
            font-size: 1.2rem;
            color: #6c757d;
            margin-bottom: 40px;
            line-height: 1.6;
        }
        
        .feature-list {
            text-align: left;
            margin: 40px 0;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            font-size: 1.1rem;
        }
        
        .feature-icon {
            color: var(--secondary-color);
            margin-right: 15px;
            width: 20px;
        }
        
        .btn-hero {
            background: var(--gradient-primary);
            border: none;
            border-radius: 50px;
            padding: 15px 40px;
            font-size: 1.1rem;
            font-weight: 600;
            color: white;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            margin: 10px;
            position: relative;
            overflow: hidden;
        }
        
        .btn-hero:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(52, 152, 219, 0.4);
            color: white;
        }
        
        .btn-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }
        
        .btn-hero:hover::before {
            left: 100%;
        }
        
        .warning-box {
            background: linear-gradient(135deg, rgba(231, 76, 60, 0.1) 0%, rgba(192, 57, 43, 0.1) 100%);
            border: 1px solid rgba(231, 76, 60, 0.3);
            border-radius: 12px;
            padding: 20px;
            margin-top: 30px;
        }
        
        .warning-title {
            color: #c0392b;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .warning-text {
            color: #e74c3c;
            font-size: 0.9rem;
            line-height: 1.5;
        }
        
        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        @media (max-width: 768px) {
            .hero-card {
                padding: 40px 20px;
                margin: 20px;
            }
            
            .hero-title {
                font-size: 2rem;
            }
            
            .hero-subtitle {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="hero-section">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8 col-xl-6">
                    <div class="hero-content">
                        <div class="hero-card">
                            <div class="hero-icon">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            
                            <h1 class="hero-title">网络安全管理系统</h1>
                            <p class="hero-subtitle">
                                企业级网络安全管理和监控平台<br>
                                提供全面的安全管理功能，保障企业信息安全
                            </p>
                            
                            <div class="feature-list">
                                <div class="feature-item">
                                    <i class="fas fa-shield-alt feature-icon"></i>
                                    <span>安全威胁监控与防护</span>
                                </div>
                                <div class="feature-item">
                                    <i class="fas fa-users feature-icon"></i>
                                    <span>用户权限管理系统</span>
                                </div>
                                <div class="feature-item">
                                    <i class="fas fa-mobile-alt feature-icon"></i>
                                    <span>现代化响应式界面设计</span>
                                </div>
                                <div class="feature-item">
                                    <i class="fas fa-chart-line feature-icon"></i>
                                    <span>安全数据分析报告</span>
                                </div>
                            </div>
                            
                            <div class="hero-actions">
                                <a href="login.php" class="btn-hero">
                                    <i class="fas fa-sign-in-alt me-2"></i>开始体验
                                </a>
                                <a href="#" class="btn-hero" target="_blank">
                                    <i class="fas fa-book me-2"></i>查看文档
                                </a>
                            </div>
                            
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
