-- 渗透测试靶场数据库初始化脚本
-- 创建数据库
CREATE DATABASE IF NOT EXISTS pentest_lab CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE pentest_lab;

-- 创建用户表（存在SQL注入漏洞）
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100),
    avatar VARCHAR(255) DEFAULT 'default.jpg',
    role ENUM('admin', 'user') DEFAULT 'user',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建管理员账户
INSERT INTO users (username, password, email, role) VALUES 
('admin', MD5('admin123'), '<EMAIL>', 'admin'),
('test', MD5('test123'), '<EMAIL>', 'user'),
('demo', MD5('demo123'), '<EMAIL>', 'user');

-- 创建文件上传记录表
CREATE TABLE IF NOT EXISTS uploads (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT,
    upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 创建系统日志表（用于记录登录尝试等）
CREATE TABLE IF NOT EXISTS system_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NULL,
    action VARCHAR(100) NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    details TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- 插入一些测试数据
INSERT INTO system_logs (action, ip_address, details) VALUES 
('system_start', '127.0.0.1', 'Pentest lab system initialized'),
('user_register', '*************', 'New user registration attempt');
